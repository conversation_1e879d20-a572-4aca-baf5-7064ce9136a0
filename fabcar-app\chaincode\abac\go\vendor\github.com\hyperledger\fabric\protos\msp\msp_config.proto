/*
Copyright IBM Corp. All Rights Reserved.

SPDX-License-Identifier: Apache-2.0
*/

syntax = "proto3";

option go_package = "github.com/hyperledger/fabric/protos/msp";
option java_package = "org.hyperledger.fabric.protos.msp";
option java_outer_classname = "MspConfigPackage";

package msp;

// MSPConfig collects all the configuration information for
// an MSP. The Config field should be unmarshalled in a way
// that depends on the Type
message MSPConfig {
    // Type holds the type of the MSP; the default one would
    // be of type FABRIC implementing an X.509 based provider
    int32 type = 1;

    // Config is MSP dependent configuration info
    bytes config = 2;
}

// FabricMSPConfig collects all the configuration information for
// a Fabric MSP.
// Here we assume a default certificate validation policy, where
// any certificate signed by any of the listed rootCA certs would
// be considered as valid under this MSP.
// This MSP may or may not come with a signing identity. If it does,
// it can also issue signing identities. If it does not, it can only
// be used to validate and verify certificates.
message FabricMSPConfig {
    // Name holds the identifier of the MSP; MSP identifier
    // is chosen by the application that governs this MSP.
    // For example, and assuming the default implementation of MSP,
    // that is X.509-based and considers a single Issuer,
    // this can refer to the Subject OU field or the Issuer OU field.
    string name = 1;

    // List of root certificates trusted by this MSP
    // they are used upon certificate validation (see
    // comment for IntermediateCerts below)
    repeated bytes root_certs = 2;

    // List of intermediate certificates trusted by this MSP;
    // they are used upon certificate validation as follows:
    // validation attempts to build a path from the certificate
    // to be validated (which is at one end of the path) and
    // one of the certs in the RootCerts field (which is at
    // the other end of the path). If the path is longer than
    // 2, certificates in the middle are searched within the
    // IntermediateCerts pool
    repeated bytes intermediate_certs = 3;

    // Identity denoting the administrator of this MSP
    repeated bytes admins = 4;

    // Identity revocation list
    repeated bytes revocation_list = 5;

    // SigningIdentity holds information on the signing identity
    // this peer is to use, and which is to be imported by the
    // MSP defined before
    SigningIdentityInfo signing_identity = 6;

    // OrganizationalUnitIdentifiers holds one or more
    // fabric organizational unit identifiers that belong to
    // this MSP configuration
    repeated FabricOUIdentifier organizational_unit_identifiers = 7;

    // FabricCryptoConfig contains the configuration parameters
    // for the cryptographic algorithms used by this MSP
    FabricCryptoConfig crypto_config = 8;

    // List of TLS root certificates trusted by this MSP.
    // They are returned by GetTLSRootCerts.
    repeated bytes tls_root_certs = 9;

    // List of TLS intermediate certificates trusted by this MSP;
    // They are returned by GetTLSIntermediateCerts.
    repeated bytes tls_intermediate_certs = 10;

    // fabric_node_ous contains the configuration to distinguish clients from peers from orderers
    // based on the OUs.
    FabricNodeOUs fabric_node_ous = 11;
}

// FabricCryptoConfig contains configuration parameters
// for the cryptographic algorithms used by the MSP
// this configuration refers to
message FabricCryptoConfig {

    // SignatureHashFamily is a string representing the hash family to be used
    // during sign and verify operations.
    // Allowed values are "SHA2" and "SHA3".
    string signature_hash_family = 1;

    // IdentityIdentifierHashFunction is a string representing the hash function
    // to be used during the computation of the identity identifier of an MSP identity.
    // Allowed values are "SHA256", "SHA384" and "SHA3_256", "SHA3_384".
    string identity_identifier_hash_function = 2;

}

// IdemixMSPConfig collects all the configuration information for
// an Idemix MSP.
message IdemixMSPConfig {
    // Name holds the identifier of the MSP
    string name = 1;

    // ipk represents the (serialized) issuer public key
    bytes ipk = 2;

    // signer may contain crypto material to configure a default signer
    IdemixMSPSignerConfig signer = 3;

    // revocation_pk is the public key used for revocation of credentials
    bytes revocation_pk = 4;

    // epoch represents the current epoch (time interval) used for revocation
    int64 epoch = 5;
}

// IdemixMSPSIgnerConfig contains the crypto material to set up an idemix signing identity
message IdemixMSPSignerConfig {
    // cred represents the serialized idemix credential of the default signer
    bytes cred = 1;

    // sk is the secret key of the default signer, corresponding to credential Cred
    bytes sk = 2;

    // organizational_unit_identifier defines the organizational unit the default signer is in
    string organizational_unit_identifier = 3;

    // role defines whether the default signer is admin, peer, member or client
    int32 role = 4;

    // enrollment_id contains the enrollment id of this signer
    string enrollment_id = 5;

    // credential_revocation_information contains a serialized CredentialRevocationInformation
    bytes credential_revocation_information = 6;
}

// SigningIdentityInfo represents the configuration information
// related to the signing identity the peer is to use for generating
// endorsements
message SigningIdentityInfo {
    // PublicSigner carries the public information of the signing
    // identity. For an X.509 provider this would be represented by
    // an X.509 certificate
    bytes public_signer = 1;

    // PrivateSigner denotes a reference to the private key of the
    // peer's signing identity
    KeyInfo private_signer = 2;
}

// KeyInfo represents a (secret) key that is either already stored
// in the bccsp/keystore or key material to be imported to the
// bccsp key-store. In later versions it may contain also a
// keystore identifier
message KeyInfo {
    // Identifier of the key inside the default keystore; this for
    // the case of Software BCCSP as well as the HSM BCCSP would be
    // the SKI of the key
    string key_identifier = 1;

    // KeyMaterial (optional) for the key to be imported; this is
    // properly encoded key bytes, prefixed by the type of the key
    bytes key_material = 2;
}

// FabricOUIdentifier represents an organizational unit and
// its related chain of trust identifier.
message FabricOUIdentifier {

    // Certificate represents the second certificate in a certification chain.
    // (Notice that the first certificate in a certification chain is supposed
    // to be the certificate of an identity).
    // It must correspond to the certificate of root or intermediate CA
    // recognized by the MSP this message belongs to.
    // Starting from this certificate, a certification chain is computed
    // and bound to the OrganizationUnitIdentifier specified
    bytes certificate = 1;

    // OrganizationUnitIdentifier defines the organizational unit under the
    // MSP identified with MSPIdentifier
    string organizational_unit_identifier = 2;
}

// FabricNodeOUs contains configuration to tell apart clients from peers from orderers
// based on OUs. If NodeOUs recognition is enabled then an msp identity
// that does not contain any of the specified OU will be considered invalid.
message FabricNodeOUs {
    // If true then an msp identity that does not contain any of the specified OU will be considered invalid.
    bool   enable = 1;

    // OU Identifier of the clients
    FabricOUIdentifier client_ou_identifier = 2;

    // OU Identifier of the peers
    FabricOUIdentifier peer_ou_identifier = 3;

}