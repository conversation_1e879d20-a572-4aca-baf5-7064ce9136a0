/*
Copyright IBM Corp. All Rights Reserved.

SPDX-License-Identifier: Apache-2.0
*/


syntax = "proto3";

option go_package = "github.com/hyperledger/fabric/protos/msp";
option java_package = "org.hyperledger.fabric.protos.msp";

package msp;

// This struct represents an Identity
// (with its MSP identifier) to be used
// to serialize it and deserialize it
message SerializedIdentity {
	// The identifier of the associated membership service provider
    string mspid = 1;

    // the Identity, serialized according to the rules of its MPS
    bytes id_bytes = 2;
}

// This struct represents an Idemix Identity
// to be used to serialize it and deserialize it.
// The IdemixMSP will first serialize an idemix identity to bytes using
// this proto, and then uses these bytes as id_bytes in SerializedIdentity
message SerializedIdemixIdentity {
    // nym_x is the X-component of the pseudonym elliptic curve point.
    // It is a []byte representation of an amcl.BIG
    // The pseudonym can be seen as a public key of the identity, it is used to verify signatures.
	bytes nym_x = 1;

    // nym_y is the Y-component of the pseudonym elliptic curve point.
    // It is a []byte representation of an amcl.BIG
    // The pseudonym can be seen as a public key of the identity, it is used to verify signatures.
	bytes nym_y = 2;

    // ou contains the organizational unit of the idemix identity
	bytes ou = 3;

    // role contains the role of this identity (e.g., ADMIN or MEMBER)
    bytes role = 4;

    // proof contains the cryptographic evidence that this identity is valid
    bytes proof = 5;
}