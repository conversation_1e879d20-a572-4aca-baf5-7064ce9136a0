{"comment": "", "ignore": "test", "package": [{"checksumSHA1": "GaJLoEuMGnP5ofXvuweAI4wx06U=", "path": "github.com/golang/protobuf/proto", "revision": "1918e1ff6ffd2be7bed0553df8650672c3bfe80d", "revisionTime": "2018-10-30T15:47:21Z"}, {"checksumSHA1": "XGpUl1X+7ly1ski4Pc+N9ozfVv8=", "path": "github.com/hyperledger/fabric/core/chaincode/shim/ext/attrmgr", "revision": "60f968db8e6e2ebcf439391610e22250993d0a85", "revisionTime": "2018-09-12T02:19:31Z"}, {"checksumSHA1": "vFuT7942CfsCcH9IG3zHmQ4d/oI=", "path": "github.com/hyperledger/fabric/core/chaincode/shim/ext/cid", "revision": "60f968db8e6e2ebcf439391610e22250993d0a85", "revisionTime": "2018-09-12T02:19:31Z"}, {"checksumSHA1": "ZzWCzHsWRI/LAxhZYUMqVcIAsZQ=", "path": "github.com/hyperledger/fabric/protos/msp", "revision": "60f968db8e6e2ebcf439391610e22250993d0a85", "revisionTime": "2018-09-12T02:19:31Z"}, {"checksumSHA1": "DTy0iJ2w5C+FDsN9EnzfhNmvS+o=", "path": "github.com/pkg/errors", "revision": "059132a15dd08d6704c67711dae0cf35ab991756", "revisionTime": "2018-10-23T23:59:46Z"}], "rootPath": "github.com/hyperledger/fabric-samples/chaincode/abac/go"}