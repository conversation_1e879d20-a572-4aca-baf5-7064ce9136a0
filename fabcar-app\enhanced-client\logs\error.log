{"level":"error","message":"Failed to connect to network: User appUser not found in wallet","service":"fabcar-client","stack":"Error: User appUser not found in wallet\n    at NetworkManager.connect (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:173:23)\n    at async main (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\query.js:174:9)","timestamp":"2025-06-24 18:27:17"}
{"level":"error","message":"Failed to connect to network: User appUser not found in wallet","service":"fabcar-client","stack":"Error: User appUser not found in wallet\n    at NetworkManager.connect (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:173:23)\n    at async main (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\invoke.js:122:9)","timestamp":"2025-06-24 18:27:34"}
{"level":"error","message":"Failed to connect to network: User appUser not found in wallet","service":"fabcar-client","stack":"Error: User appUser not found in wallet\n    at NetworkManager.connect (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:173:23)\n    at async initializeNetwork (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\app.js:32:9)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\app.js:294:9)","timestamp":"2025-06-24 18:29:00"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"fabcar-client","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-24 18:32:09","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"fabcar-client","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-24 18:32:32","type":"entity.parse.failed"}
{"level":"error","message":"Failed to connect to network: User appUser not found in wallet","service":"fabcar-client","stack":"Error: User appUser not found in wallet\n    at NetworkManager.connect (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:173:23)\n    at async initializeNetwork (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\app.js:32:9)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\app.js:294:9)","timestamp":"2025-06-26 05:27:30"}
{"code":"ECONNREFUSED","level":"error","message":"Failed to enroll admin: Calling enroll endpoint failed with error [AggregateError]","service":"fabcar-client","stack":"Error: Calling enroll endpoint failed with error [AggregateError]\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\fabric-ca-client\\lib\\FabricCAClient.js:327:19)\n    at ClientRequest.emit (node:events:507:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:507:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-30 18:14:53"}
{"level":"error","message":"Failed to register user appUser: gateway.getClient is not a function","service":"fabcar-client","stack":"TypeError: gateway.getClient is not a function\n    at NetworkManager.registerUser (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:126:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async main (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\registerUser.js:22:9)","timestamp":"2025-06-30 18:17:33"}
{"level":"error","message":"Failed to connect to network: User appUser not found in wallet","service":"fabcar-client","stack":"Error: User appUser not found in wallet\n    at NetworkManager.connect (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:173:23)\n    at async main (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\query.js:174:9)","timestamp":"2025-06-30 18:17:47"}
