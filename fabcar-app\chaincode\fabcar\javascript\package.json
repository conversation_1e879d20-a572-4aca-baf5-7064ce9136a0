{"name": "fabcar", "version": "1.0.0", "description": "FabCar contract implemented in JavaScript", "main": "index.js", "engines": {"node": ">=8", "npm": ">=5"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --recursive", "start": "fabric-chaincode-node start"}, "engineStrict": true, "author": "Hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-contract-api": "~1.4.0", "fabric-shim": "~1.4.0"}, "devDependencies": {"chai": "^4.1.2", "eslint": "^4.19.1", "mocha": "^5.2.0", "nyc": "^12.0.2", "sinon": "^6.0.0", "sinon-chai": "^3.2.0"}, "nyc": {"exclude": ["coverage/**", "test/**"], "reporter": ["text-summary", "html"], "all": true, "check-coverage": true, "statements": 100, "branches": 100, "functions": 100, "lines": 100}}